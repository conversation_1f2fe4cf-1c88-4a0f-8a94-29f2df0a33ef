import React, { useState, useMemo, memo, useCallback, useEffect } from "react";
import { Search, BarChart3, Edit, Trash2, Eye, EyeOff, Filter, Download, ChevronLeft, ChevronRight, ArrowUp, ArrowDown, ChevronDown } from "lucide-react";
import { type KpiLogisticaData } from "@/app/actions/kpis-logistica";
import ConfirmDialog from "@/components/ui/ConfirmDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  role?: string;
}

interface KpiLogisticaHistorySectionProps {
  kpisLogistica: KpiLogisticaData[];
  loadingKpis: boolean;
  user: User;
  onEditKpi: (kpi: KpiLogisticaData) => void;
  onDeleteKpi: (kpiId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
  showAnalytics?: boolean;
  showFilters?: boolean;
  selectedKpis?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceBelow = viewport.height - container.bottom;
    const spaceAbove = container.top;
    const tooltipHeight = tooltip.height || 80;

    const showAbove = spaceBelow < tooltipHeight + 20 && spaceAbove > tooltipHeight + 20;

    // Determinar posición horizontal (centrado, izquierda o derecha)
    const containerCenter = container.left + container.width / 2;
    const tooltipWidth = tooltip.width || 280;
    const halfTooltipWidth = tooltipWidth / 2;
    const margin = 20;

    let horizontalPosition = 'center';
    if (containerCenter - halfTooltipWidth < margin) {
      horizontalPosition = 'left';
    } else if (containerCenter + halfTooltipWidth > viewport.width - margin) {
      horizontalPosition = 'right';
    }

    setPosition({
      top: !showAbove,
      left: horizontalPosition === 'left',
      right: horizontalPosition === 'right',
      center: horizontalPosition === 'center'
    });
  }, []);

  React.useEffect(() => {
    if (isVisible) {
      updatePosition();
    }
  }, [isVisible, updatePosition]);

  const getTooltipClasses = () => {
    let classes = "absolute px-3 py-2 text-xs font-medium text-white rounded-md shadow-lg pointer-events-none transition-opacity duration-200";

    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-0";
    } else if (position.right) {
      classes += " right-0";
    }

    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-0 h-0";

    if (position.top) {
      classes += " border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent";
      classes += " -top-1";
    } else {
      classes += " border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent";
      classes += " -bottom-1";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-3";
    } else if (position.right) {
      classes += " right-3";
    }

    return classes;
  };

  const getArrowStyle = () => {
    const borderColor = 'rgba(0, 0, 0, 0.8)';

    if (position.top) {
      return { borderBottomColor: borderColor };
    } else {
      return { borderTopColor: borderColor };
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '200px',
            width: 'max-content',
            whiteSpace: 'pre-line',
            wordWrap: 'break-word',
            lineHeight: '1.3',
            maxHeight: '150px',
            overflowY: 'visible'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

// Función para formatear fechas
const formatDate = (dateString: string | Date) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit'
  });
};

// Función para formatear números con separadores de miles (formato mexicano)
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('en-US').format(num);
};

// Función para formatear números con decimales y separadores de miles (formato mexicano)
const formatNumberWithDecimals = (num: number, decimals: number = 1) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(num);
};

// Función para formatear moneda con separadores de miles (formato mexicano)
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2
  }).format(amount);
};

const KpiLogisticaHistorySection: React.FC<KpiLogisticaHistorySectionProps> = ({
  kpisLogistica,
  loadingKpis,
  user,
  onEditKpi,
  onDeleteKpi,
  onRefresh,
  showAnalytics: externalShowAnalytics = false,
  showFilters: externalShowFilters = false,
  selectedKpis: externalSelectedKpis = [],
  onSelectionChange
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedYear, setSelectedYear] = useState<number | "all">("all");
  const [sortField, setSortField] = useState<keyof KpiLogisticaData>("weekNumber");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedKpis, setSelectedKpis] = useState<string[]>(externalSelectedKpis);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [kpiToDelete, setKpiToDelete] = useState<KpiLogisticaData | null>(null);
  const [openDropdowns, setOpenDropdowns] = useState<Set<string>>(new Set());

  // Sincronizar con props externas
  useEffect(() => {
    setSelectedKpis(externalSelectedKpis);
  }, [externalSelectedKpis]);

  // Obtener años únicos para el filtro
  const availableYears = useMemo(() => {
    const years = [...new Set(kpisLogistica.map(kpi => kpi.year))].sort((a, b) => b - a);
    return years;
  }, [kpisLogistica]);

  // Filtrar y ordenar KPIs
  const filteredAndSortedKpis = useMemo(() => {
    let filtered = kpisLogistica.filter(kpi => {
      const matchesSearch = searchTerm === "" || 
        `Semana ${kpi.weekNumber}/${kpi.year}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kpi.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kpi.user?.email?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesYear = selectedYear === "all" || kpi.year === selectedYear;
      
      return matchesSearch && matchesYear;
    });

    // Ordenar
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Manejar casos especiales para ordenamiento
      if (sortField === 'user') {
        aValue = a.user?.name || a.user?.email || '';
        bValue = b.user?.name || b.user?.email || '';
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    return filtered;
  }, [kpisLogistica, searchTerm, selectedYear, sortField, sortDirection]);

  // Paginación
  const totalPages = Math.ceil(filteredAndSortedKpis.length / itemsPerPage);
  const paginatedKpis = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredAndSortedKpis.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredAndSortedKpis, currentPage, itemsPerPage]);

  // Funciones de manejo
  const handleSort = (field: keyof KpiLogisticaData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleSelectKpi = useCallback((kpiId: string) => {
    const newSelection = selectedKpis.includes(kpiId)
      ? selectedKpis.filter(id => id !== kpiId)
      : [...selectedKpis, kpiId];
    
    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [selectedKpis, onSelectionChange]);

  const handleSelectAll = useCallback(() => {
    const currentPageIds = paginatedKpis.map(kpi => kpi.id!);
    const allSelected = currentPageIds.every(id => selectedKpis.includes(id));
    
    let newSelection;
    if (allSelected) {
      newSelection = selectedKpis.filter(id => !currentPageIds.includes(id));
    } else {
      newSelection = [...new Set([...selectedKpis, ...currentPageIds])];
    }
    
    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [paginatedKpis, selectedKpis, onSelectionChange]);

  const handleDeleteConfirmation = (kpi: KpiLogisticaData) => {
    setKpiToDelete(kpi);
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (kpiToDelete?.id) {
      await onDeleteKpi(kpiToDelete.id);
      setShowDeleteConfirm(false);
      setKpiToDelete(null);
    }
  };

  // Resetear página cuando cambian los filtros
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedYear]);

  // Calcular datos de analytics
  const analyticsData = useMemo(() => {
    if (kpisLogistica.length === 0) return null;

    // Encontrar mejor y peor semana por porcentaje de entregas a tiempo
    const mejorSemana = kpisLogistica.reduce((prev, current) =>
      current.porcentajeEntregasTiempo > prev.porcentajeEntregasTiempo ? current : prev
    );

    const peorSemana = kpisLogistica.reduce((prev, current) =>
      current.porcentajeEntregasTiempo < prev.porcentajeEntregasTiempo ? current : prev
    );

    // Calcular promedios
    const promedios = {
      porcentajeEntregasTiempo: kpisLogistica.reduce((sum, kpi) => sum + kpi.porcentajeEntregasTiempo, 0) / kpisLogistica.length,
      porcentajeRetardos: kpisLogistica.reduce((sum, kpi) => sum + kpi.porcentajeRetardos, 0) / kpisLogistica.length,
      porcentajeReprogramaciones: kpisLogistica.reduce((sum, kpi) => sum + kpi.porcentajeReprogramaciones, 0) / kpisLogistica.length,
      promedioCostoFleteLitro: kpisLogistica.reduce((sum, kpi) => sum + kpi.promedioCostoFleteLitro, 0) / kpisLogistica.length,
      porcentajeRutasCotizadas: kpisLogistica.reduce((sum, kpi) => sum + kpi.porcentajeRutasCotizadas, 0) / kpisLogistica.length,
      porcentajeTransportistas: kpisLogistica.reduce((sum, kpi) => sum + kpi.porcentajeTransportistas, 0) / kpisLogistica.length
    };

    return {
      mejorSemana,
      peorSemana,
      promedios,
      totalRegistros: kpisLogistica.length
    };
  }, [kpisLogistica]);

  // Componente de fila de tabla memoizado para mejor rendimiento
  const TableRow = memo(({
    kpi,
    isSelected,
    onSelect,
    onEdit,
    onDelete,
    user: currentUser
  }: {
    kpi: KpiLogisticaData;
    isSelected: boolean;
    onSelect: (id: string) => void;
    onEdit: (kpi: KpiLogisticaData) => void;
    onDelete: (id: string) => void;
    user: User;
  }) => {
    return (
      <tr className="bg-white shadow hover:shadow-md transition-all duration-200 rounded-xl border border-gray-200" style={{ overflow: 'visible' }}>
        <td className="px-4 py-3 rounded-l-xl">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect(kpi.id!)}
            className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
          />
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="flex items-center space-x-3">
            <div>
              <div className="text-sm font-medium text-gray-900">
                Semana {kpi.weekNumber}/{kpi.year}
              </div>
              <div className="text-xs text-gray-500">
                {formatDate(kpi.weekStartDate)} - {formatDate(kpi.weekEndDate)}
              </div>
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            <div className="font-medium">{formatNumber(kpi.unidadesConfirmadas)} / {formatNumber(kpi.unidadesSolicitadas)}</div>
            <div className="text-xs text-gray-500">
              {((kpi.unidadesConfirmadas / kpi.unidadesSolicitadas) * 100).toFixed(1)}% confirmadas
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            <div className="font-medium text-green-600">{kpi.porcentajeEntregasTiempo.toFixed(1)}%</div>
            <div className="text-xs text-gray-500">
              {kpi.porcentajeRetardos.toFixed(1)}% retardos
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium text-orange-600">
            {kpi.porcentajeReprogramaciones.toFixed(1)}%
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium">
            {formatNumberWithDecimals(kpi.promedioKmOperacion)} km
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium">
            {formatCurrency(kpi.promedioCostoFleteLitro)}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium">
            {formatCurrency(kpi.promedioCostoFleteOperacion)}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium">
            {formatCurrency(kpi.pagoSemanalFlete)}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium text-red-600">
            {formatCurrency(kpi.pagoSemanalPenalizaciones)}
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium text-blue-600">
            {kpi.porcentajeRutasCotizadas.toFixed(1)}%
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900 font-medium">
            {kpi.porcentajeTransportistas.toFixed(1)}%
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            <div className="font-medium">{kpi.user?.name || 'Usuario'}</div>
            <div className="text-xs text-gray-500">
              {formatDate(kpi.createdAt || new Date().toISOString())}
            </div>
          </div>
        </td>
        <td className="px-4 py-3 whitespace-nowrap text-right rounded-r-lg">
          <div className="flex items-center justify-end space-x-1">
            <button
              onClick={() => onEdit(kpi)}
              className="p-1.5 text-gray-400 hover:text-primary hover:bg-primary/10 rounded transition-colors"
              title="Editar KPI"
            >
              <Edit className="h-4 w-4" />
            </button>
            {(currentUser.role === "ADMIN" || currentUser.role === "SUPER_ADMIN") && (
              <button
                onClick={() => onDelete(kpi.id!)}
                className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                title="Eliminar KPI"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>
        </td>
      </tr>
    );
  });

  return (
    <div className="overflow-hidden">
      {/* Filters */}
      {externalShowFilters && (
        <div className="pt-3 pb-3 px-2">
          <div className="flex ">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl w-full">
              <div className="flex flex-col h-full md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Buscar
                </label>
                <div className="relative w-full flex-grow">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.3-4.3"></path>
                  </svg>
                  <input
                    placeholder="Buscar por semana.."
                    className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent h-10"
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex flex-col h-full">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Año
                </label>
                <Select
                  value={selectedYear.toString()}
                  onValueChange={(value) => setSelectedYear(value === "all" ? "all" : parseInt(value))}
                >
                  <SelectTrigger className="flex items-center justify-between rounded-md bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                    <SelectValue placeholder="Todos los años" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los años</SelectItem>
                    {availableYears.map(year => (
                      <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col h-full justify-end">
                <button
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedYear("all");
                    setCurrentPage(1);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors h-10"
                >
                  Limpiar filtros
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics */}
      {externalShowAnalytics && analyticsData && (
        <div className="py-4">
          {/* Mejor y Peor Semana */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="bg-white rounded-lg p-3 border shadow-md" title="Semana con el mayor porcentaje de entregas a tiempo">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900">Mejor Semana</div>
                  <div className="text-xs text-gray-500">Semana {analyticsData.mejorSemana.weekNumber}/{analyticsData.mejorSemana.year}</div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold" style={{ color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' }}>
                    {analyticsData.mejorSemana.porcentajeEntregasTiempo.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-500">Entregas a tiempo</div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border p-3 shadow-md" title="Semana con el menor porcentaje de entregas a tiempo">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900">Semana a Mejorar</div>
                  <div className="text-xs text-gray-500">Semana {analyticsData.peorSemana.weekNumber}/{analyticsData.peorSemana.year}</div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-red-600">
                    {analyticsData.peorSemana.porcentajeEntregasTiempo.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-500">Entregas a tiempo</div>
                </div>
              </div>
            </div>
          </div>

          {/* Métricas Promedio */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 pt-4">
            <div className="text-center" title="Promedio del porcentaje de entregas a tiempo de todas las semanas">
              <div className="text-lg font-semibold text-gray-900">
                {analyticsData.promedios.porcentajeEntregasTiempo.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Entregas a Tiempo Promedio</div>
            </div>
            <div className="text-center" title="Promedio del porcentaje de retardos">
              <div className="text-lg font-semibold text-gray-900">
                {analyticsData.promedios.porcentajeRetardos.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Retardos Promedio</div>
            </div>
            <div className="text-center" title="Promedio del costo de flete por litro">
              <div className="text-lg font-semibold text-gray-900">
                {formatCurrency(analyticsData.promedios.promedioCostoFleteLitro)}
              </div>
              <div className="text-xs text-gray-500">Costo Promedio por Litro</div>
            </div>
            <div className="text-center" title="Promedio del porcentaje de rutas cotizadas">
              <div className="text-lg font-semibold text-gray-900">
                {analyticsData.promedios.porcentajeRutasCotizadas.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Rutas Cotizadas Promedio</div>
            </div>
            <div className="text-center" title="Número de semanas donde las entregas a tiempo fueron igual o superior al 95%">
              <div className="text-lg font-semibold" style={{ color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' }}>
                {kpisLogistica.filter(kpi => kpi.porcentajeEntregasTiempo >= 95).length}
              </div>
              <div className="text-xs text-gray-500">Semanas con Entregas Excelentes (≥95%)</div>
            </div>
            <div className="text-center" title="Número de semanas donde las entregas a tiempo fueron inferiores al 80%">
              <div className="text-lg font-semibold text-red-600">
                {kpisLogistica.filter(kpi => kpi.porcentajeEntregasTiempo < 80).length}
              </div>
              <div className="text-xs text-gray-500">Semanas que Necesitan Mejora (&lt;80%)</div>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {kpisLogistica.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-3">
            <BarChart3 className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay KPIs registrados</h3>
          <p className="text-gray-500 mb-2">Comienza agregando datos semanales para ver el historial</p>
        </div>
      ) : filteredAndSortedKpis.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron resultados</h3>
          <p className="text-gray-500 mb-4">Intenta ajustar los filtros de búsqueda</p>
          <button
            onClick={() => {
              setSearchTerm("");
              setSelectedYear("all");
              setCurrentPage(1);
            }}
            className="text-primary hover:text-primary/80 font-medium"
          >
            Limpiar filtros
          </button>
        </div>
      ) : (
        <>
          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full border-separate border-spacing-y-2" style={{ minWidth: '1400px' }}>
              <thead>
                <tr>
                  <th className="px-4 py-1 text-left bg-transparent">
                    <input
                      type="checkbox"
                      checked={paginatedKpis.length > 0 && paginatedKpis.every(kpi => selectedKpis.includes(kpi.id!))}
                      ref={(input) => {
                        if (input) {
                          const currentPageIds = paginatedKpis.map(kpi => kpi.id!);
                          const selectedCurrentPage = currentPageIds.filter(id => selectedKpis.includes(id));
                          input.indeterminate = selectedCurrentPage.length > 0 && selectedCurrentPage.length < currentPageIds.length;
                        }
                      }}
                      onChange={handleSelectAll}
                      className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
                    />
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <button
                      onClick={() => handleSort('weekNumber')}
                      className="flex items-center space-x-1 hover:text-gray-600"
                    >
                      <span>Semana</span>
                      {sortField === 'weekNumber' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                      )}
                    </button>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Unidades Confirmadas vs Solicitadas">
                      <span>Unidades</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Porcentaje de Entregas a Tiempo">
                      <span>% Entregas</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Porcentaje de Reprogramaciones">
                      <span>% Reprog.</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Promedio de Kilómetros por Operación">
                      <span>Km/Op</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Costo Promedio de Flete por Litro">
                      <span>Costo/L</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Costo Promedio de Flete por Operación">
                      <span>Costo/Op</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Pago Semanal de Flete">
                      <span>Pago Flete</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Pago Semanal por Penalizaciones">
                      <span>Penalizaciones</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Porcentaje de Rutas Cotizadas">
                      <span>% Rutas</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Porcentaje de Transportistas Clasificados">
                      <span>% Transport.</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Usuario que registró los datos">
                      <span>Usuario</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-right text-sm font-normal text-gray-400 bg-transparent">
                    <span>Acciones</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedKpis.map((kpi) => (
                  <TableRow
                    key={kpi.id}
                    kpi={kpi}
                    isSelected={selectedKpis.includes(kpi.id!)}
                    onSelect={handleSelectKpi}
                    onEdit={onEditKpi}
                    onDelete={() => handleDeleteConfirmation(kpi)}
                    user={user}
                  />
                ))}
              </tbody>
            </table>
          </div>

          {/* Paginación */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Mostrando {((currentPage - 1) * itemsPerPage) + 1} a {Math.min(currentPage * itemsPerPage, filteredAndSortedKpis.length)} de {filteredAndSortedKpis.length} resultados
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <span className="px-3 py-1 text-sm font-medium">
                  {currentPage} de {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleConfirmDelete}
        title="Eliminar KPI de Logística"
        message={`¿Estás seguro de que deseas eliminar los datos de la Semana ${kpiToDelete?.weekNumber}/${kpiToDelete?.year}? Esta acción no se puede deshacer.`}
        confirmText="Eliminar"
        cancelText="Cancelar"
        type="danger"
      />
    </div>
  );
};

export default KpiLogisticaHistorySection;
